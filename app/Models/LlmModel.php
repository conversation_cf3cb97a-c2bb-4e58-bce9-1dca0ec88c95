<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class LlmModel extends Model
{
    protected $fillable = [
        'uuid',
        'name',
        'display_name',
        'description',
        'huggingface_id',
        'huggingface_url',
        'huggingface_metadata',
        'has_reasoning',
        'has_tool_usage',
        'has_vision',
        'has_code_generation',
        'has_function_calling',
        'context_window',
        'parameter_count',
        'parameter_count_numeric',
        'architecture',
        'quantization',
        'format',
        'chat_template',
        'prompt_templates',
        'model_size_bytes',
        'estimated_vram_gb',
        'benchmark_score',
        'benchmark_details',
        'license',
        'commercial_use',
        'tags',
        'status',
        'last_crawled_at',
        'huggingface_updated_at',
        'crawl_metadata',
    ];

    protected $casts = [
        'huggingface_metadata' => 'array',
        'has_reasoning' => 'boolean',
        'has_tool_usage' => 'boolean',
        'has_vision' => 'boolean',
        'has_code_generation' => 'boolean',
        'has_function_calling' => 'boolean',
        'context_window' => 'integer',
        'parameter_count_numeric' => 'integer',
        'prompt_templates' => 'array',
        'model_size_bytes' => 'integer',
        'estimated_vram_gb' => 'integer',
        'benchmark_score' => 'decimal:2',
        'benchmark_details' => 'array',
        'commercial_use' => 'boolean',
        'tags' => 'array',
        'last_crawled_at' => 'datetime',
        'huggingface_updated_at' => 'datetime',
        'crawl_metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Get responses that used this model
     */
    public function responses(): HasMany
    {
        return $this->hasMany(Response::class, 'model', 'name');
    }

    /**
     * Scope to filter by active models
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to filter by capabilities
     */
    public function scopeWithCapability($query, string $capability)
    {
        $capabilityField = 'has_' . $capability;
        return $query->where($capabilityField, true);
    }

    /**
     * Scope to filter by architecture
     */
    public function scopeByArchitecture($query, string $architecture)
    {
        return $query->where('architecture', $architecture);
    }

    /**
     * Scope to filter by parameter count range
     */
    public function scopeByParameterRange($query, int $min = null, int $max = null)
    {
        if ($min !== null) {
            $query->where('parameter_count_numeric', '>=', $min);
        }
        if ($max !== null) {
            $query->where('parameter_count_numeric', '<=', $max);
        }
        return $query;
    }

    /**
     * Get formatted model size
     */
    public function getFormattedSizeAttribute(): string
    {
        if (!$this->model_size_bytes) {
            return 'Unknown';
        }

        $bytes = $this->model_size_bytes;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get capabilities as array
     */
    public function getCapabilitiesAttribute(): array
    {
        $capabilities = [];
        
        if ($this->has_reasoning) $capabilities[] = 'reasoning';
        if ($this->has_tool_usage) $capabilities[] = 'tool_usage';
        if ($this->has_vision) $capabilities[] = 'vision';
        if ($this->has_code_generation) $capabilities[] = 'code_generation';
        if ($this->has_function_calling) $capabilities[] = 'function_calling';
        
        return $capabilities;
    }

    /**
     * Extract Hugging Face ID from model name
     */
    public static function extractHuggingFaceId(string $modelName): ?string
    {
        // Handle formats like "hf.co/unsloth/Qwen3-4B-GGUF:Q8_0"
        if (str_starts_with($modelName, 'hf.co/')) {
            $parts = explode(':', str_replace('hf.co/', '', $modelName));
            return $parts[0] ?? null;
        }
        
        return null;
    }

    /**
     * Parse parameter count to numeric value
     */
    public static function parseParameterCount(string $parameterCount): ?int
    {
        $parameterCount = strtoupper(trim($parameterCount));
        
        if (preg_match('/(\d+(?:\.\d+)?)([KMBT]?)/', $parameterCount, $matches)) {
            $number = (float) $matches[1];
            $unit = $matches[2] ?? '';
            
            return match ($unit) {
                'K' => (int) ($number * 1000),
                'M' => (int) ($number * 1000000),
                'B' => (int) ($number * 1000000000),
                'T' => (int) ($number * 1000000000000),
                default => (int) $number,
            };
        }
        
        return null;
    }

    /**
     * Check if model needs metadata update
     */
    public function needsMetadataUpdate(): bool
    {
        if (!$this->last_crawled_at) {
            return true;
        }
        
        // Update if last crawl was more than 7 days ago
        return $this->last_crawled_at->lt(now()->subDays(7));
    }
}
